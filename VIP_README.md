# 🎮 会员购买系统

一个完整的会员购买系统，采用像素风格设计，支持多端自适应，集成了完整的支付流程和会员注册功能。

## ✨ 功能特点

- 🎨 **像素风格设计** - 使用浅色系配色（蓝色科技感、橙色活力感、紫色专业感）
- 📱 **多端自适应** - 完美适配桌面端、平板和手机
- 💳 **完整支付流程** - 支持微信支付和支付宝
- 🔒 **防重复注册** - 数据库级别防止重复注册
- 🛡️ **用户友好** - 多处客服投诉入口，安抚用户情绪
- 📊 **实时状态** - 支付状态实时检测
- 🎯 **二维码支付** - 自动生成支付二维码

## 📁 文件结构

```
├── vip.html                    # 主页面
├── test_vip.html              # 测试页面
├── Public/
│   └── static/
│       ├── css/
│       │   └── vip.css        # 样式文件
│       └── js/
│           └── vip.js         # 脚本文件
├── api/
│   ├── config.php             # 配置文件
│   ├── init_database.php      # 数据库初始化
│   ├── create_order.php       # 创建订单API
│   ├── check_payment.php      # 检查支付API
│   └── register_member.php    # 注册会员API
├── database/
│   └── vip_orders.sql         # 数据库表结构
└── logs/                      # 日志目录（自动创建）
```

## 🚀 部署步骤

### 1. 环境要求
- PHP 7.0+
- MySQL 5.7+
- Web服务器（Apache/Nginx）
- 支持curl扩展

### 2. 配置数据库
编辑 `api/config.php` 文件，修改数据库连接信息：
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database_name');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 3. 初始化数据库
访问 `api/init_database.php` 或运行 `database/vip_orders.sql` 创建数据表。

### 4. 设置权限
确保 `logs/` 目录有写入权限：
```bash
chmod 755 logs/
```

### 5. 测试系统
访问 `test_vip.html` 进行系统测试。

## 💰 商品配置

系统支持两种会员套餐：

| 套餐类型 | 商品ID | 价格 | 时长 | 描述 |
|---------|--------|------|------|------|
| 月度会员 | 85 | ¥28.05 | 38天 | 买一月送一周 |
| 永久会员 | 86 | ¥38.05 | 永久 | 终身使用，永不到期 |

## 🔄 业务流程

1. **选择套餐** - 用户选择月度或永久会员
2. **选择支付方式** - 微信支付或支付宝
3. **创建订单** - 调用上游API创建订单
4. **扫码支付** - 显示二维码，用户扫码支付
5. **检测支付** - 实时检测支付状态
6. **注册会员** - 支付成功后自动注册会员
7. **显示结果** - 显示会员信息和Token

## 🛠️ API接口

### 创建订单
```
GET api/create_order.php
参数：
- customer_contact: 客户联系方式
- product_id: 商品ID (85/86)
- pay_type: 支付方式 (wxpay/alipay)
```

### 检查支付状态
```
GET api/check_payment.php
参数：
- order_id: 订单ID
```

### 注册会员
```
GET api/register_member.php
参数：
- order_id: 订单ID
- lx: 会员类型 (1=月度, 2=永久)
```

## 🔧 配置说明

### 上游API配置
在 `api/config.php` 中配置上游API地址：
```php
define('UPSTREAM_CREATE_ORDER_URL', 'https://cloudshop.qnm6.top/create_order.php');
define('UPSTREAM_CHECK_PAYMENT_URL', 'https://cloudshop.qnm6.top/check_payment_status.php');
define('UPSTREAM_REGISTER_MEMBER_URL', 'https://api.qnm6.top/admapi/vipregister.php');
```

### 客服联系方式
- Telegram: https://t.me/Dataso
- 投诉地址: https://cloudshop.qnm6.top/tousu.html

## 🎨 样式定制

系统使用CSS变量，可以轻松定制颜色：
- 主色调：蓝色 (#2196f3)
- 辅助色：紫色 (#9c27b0)
- 强调色：橙色 (#ff9800)
- 成功色：绿色 (#4caf50)

## 📱 响应式断点

- 桌面端：> 768px
- 平板端：768px - 480px
- 手机端：< 480px

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置信息
   - 确保数据库服务正常运行

2. **API调用失败**
   - 检查网络连接
   - 验证上游API地址

3. **二维码不显示**
   - 确保qrcode.js文件存在
   - 检查浏览器控制台错误

4. **支付状态检测失败**
   - 检查订单ID是否正确
   - 验证上游API响应

## 📝 日志系统

系统会自动记录关键操作日志到 `logs/vip_api.log`：
- 订单创建
- 支付状态检查
- 会员注册
- 错误信息

## 🔐 安全考虑

- 所有API调用都有参数验证
- 防止SQL注入攻击
- 订单重复注册检查
- 错误信息不暴露敏感数据

## 📞 技术支持

如遇到技术问题，请：
1. 查看日志文件
2. 使用测试页面诊断
3. 联系技术支持

---

**注意：** 本系统仅供学习和测试使用，生产环境部署前请进行充分测试。
