<?php
namespace Index\Controller;
use Think\Controller;
class UtilsController extends Controller {
    public function index(){

    	// 获取所有工具数据用于工具库页面展示
    	$all_tools = M("app")->order("addtime desc")->select();
		$this->assign("all_tools",$all_tools);

    	// 获取配置信息
		$con =M("config")->where("id=1")->find();
		$this->assign("con",$con);
		$this->display("index");
    }
    
	    public function type(){
	    
		    $id = $_GET['id'];
		    $mod = M("app")->where("type={$id}")->order("addtime desc")->select();
		    $this->assign("mod",$mod);
		    $type = M("type")->select();
	    	$this->assign("type",$type);
	    	$con =M("config")->where("id=1")->find()['webname'];
			 $this->assign("con",$con);
		    $this->display("type");
	    
	    }
	    
	     public function h5(){
        	$type = M("type")->select();
    	  $this->assign("type",$type);
        	$m=M('type');  
			$m1=M('app');  
			$parent=$m->order('sort DESC')->select();  
			 foreach($parent as $n=> $val){  
				 $id = $val['id'];
				 $parent[$n]['list']=$m1->where("type='{$id}'")->order("addtime DESC")->limit(10)->select();  
			 } 
			 $this->assign("app",$parent);
			 $con =M("config")->where("id=1")->find()['webname'];
			 $this->assign("con",$con);
         $this->display("h5");
    	}
    	
    	public function h5t(){
        	 $id = $_GET['id'];
		    $mod = M("app")->where("type={$id}")->order("addtime desc")->select();
		    $this->assign("mod",$mod);
		    $type = M("type")->select();
	    	$this->assign("type",$type);
	    	$con =M("config")->where("id=1")->find()['webname'];
			 $this->assign("con",$con);
		    $this->display("h5t");
    	}

    	public function vip(){
    		// 获取配置信息
			$con =M("config")->where("id=1")->find();
			$this->assign("con",$con);
			$this->display("vip");
    	}

    	/**
    	 * 创建订单代理接口
    	 */
    	public function createOrder(){
    		// 只允许POST请求
    		if(!IS_POST){
    			$this->ajaxReturn(array('status' => 'error', 'message' => '请求方式错误'));
    			return;
    		}

    		// 基础安全检查
    		if(!$this->securityCheck()){
    			$this->ajaxReturn(array('status' => 'error', 'message' => '请求被拒绝'));
    			return;
    		}

    		// 获取参数
    		$customerContact = I('post.customer_contact', '<EMAIL>', 'trim');
    		$productId = I('post.product_id', '', 'trim');
    		$payType = I('post.pay_type', '', 'trim');

    		// 参数验证
    		if(empty($productId) || empty($payType)){
    			$this->ajaxReturn(array('status' => 'error', 'message' => '参数不完整'));
    			return;
    		}

    		// 验证产品ID
    		if(!in_array($productId, ['85', '86'])){
    			$this->ajaxReturn(array('status' => 'error', 'message' => '产品ID无效'));
    			return;
    		}

    		// 验证支付类型
    		if(!in_array($payType, ['wxpay', 'alipay'])){
    			$this->ajaxReturn(array('status' => 'error', 'message' => '支付类型无效'));
    			return;
    		}

    		// 调用创建订单API
    		try {
    			$params = array(
    				'customer_contact' => $customerContact,
    				'product_id' => $productId,
    				'pay_type' => $payType
    			);

    			$url = "https://cloudshop.qnm6.top/create_order.php?" . http_build_query($params);
    			$response = $this->httpGet($url);
    			$data = json_decode($response, true);

    			// 记录日志
    			$this->logApiCall('createOrder', $params, $data);

    			if($data && isset($data['status'])){
    				$this->ajaxReturn($data);
    			} else {
    				$result = array('status' => 'error', 'message' => '订单创建失败');
    				$this->logApiCall('createOrder_error', $params, $result);
    				$this->ajaxReturn($result);
    			}
    		} catch (Exception $e) {
    			$result = array('status' => 'error', 'message' => '订单服务异常');
    			$this->logApiCall('createOrder_exception', array('product_id' => $productId), $result);
    			$this->ajaxReturn($result);
    		}
    	}

    	/**
    	 * 支付状态检查代理接口
    	 */
    	public function checkPayment(){
    		// 只允许POST请求
    		if(!IS_POST){
    			$this->ajaxReturn(array('status' => 'error', 'message' => '请求方式错误'));
    			return;
    		}

    		// 基础安全检查
    		if(!$this->securityCheck()){
    			$this->ajaxReturn(array('status' => 'error', 'message' => '请求被拒绝'));
    			return;
    		}

    		// 获取订单号
    		$orderId = I('post.order_id', '', 'trim');
    		if(empty($orderId)){
    			$this->ajaxReturn(array('status' => 'error', 'message' => '订单号不能为空'));
    			return;
    		}

    		// 调用支付状态检查API
    		try {
    			$url = "https://cloudshop.qnm6.top/check_payment_status.php?order_id=" . urlencode($orderId);
    			$response = $this->httpGet($url);
    			$data = json_decode($response, true);

    			if($data && isset($data['status'])){
    				$this->ajaxReturn($data);
    			} else {
    				$this->ajaxReturn(array('status' => 'error', 'message' => '支付状态查询失败'));
    			}
    		} catch (Exception $e) {
    			$this->ajaxReturn(array('status' => 'error', 'message' => '查询服务异常'));
    		}
    	}

    	/**
    	 * 会员注册代理接口
    	 * 作为前端和真实API之间的安全代理
    	 */
    	public function registerVip(){
    		// 只允许POST请求
    		if(!IS_POST){
    			$this->ajaxReturn(array('code' => 400, 'message' => '请求方式错误'));
    			return;
    		}

    		// 基础安全检查
    		if(!$this->securityCheck()){
    			$this->ajaxReturn(array('code' => 400, 'message' => '请求被拒绝'));
    			return;
    		}

    		// 获取参数
    		$orderId = I('post.order_id', '', 'trim');
    		$productType = I('post.product_type', '', 'trim');

    		// 参数验证
    		if(empty($orderId) || empty($productType)){
    			$this->ajaxReturn(array('code' => 400, 'message' => '参数不完整'));
    			return;
    		}

    		// 验证产品类型
    		if(!in_array($productType, ['1', '2'])){
    			$this->ajaxReturn(array('code' => 400, 'message' => '产品类型错误'));
    			return;
    		}

    		// 检查订单是否已经处理过
    		$orderModel = M('order_status');
    		$existingOrder = $orderModel->where(array('order_id' => $orderId))->find();

    		if($existingOrder){
    			if($existingOrder['status'] == 'completed'){
    				$this->ajaxReturn(array('code' => 400, 'message' => '该订单已经处理过，不能重复注册'));
    				return;
    			} else if($existingOrder['status'] == 'processing'){
    				$this->ajaxReturn(array('code' => 400, 'message' => '该订单正在处理中，请稍后再试'));
    				return;
    			}
    		}

    		// 验证订单状态（先验证订单是否已支付）
    		$paymentStatus = $this->checkOrderPaymentStatus($orderId);
    		if(!$paymentStatus){
    			$this->ajaxReturn(array('code' => 400, 'message' => '订单未支付或不存在'));
    			return;
    		}

    		// 标记订单为处理中状态
    		$this->markOrderAsProcessing($orderId, $productType);

    		// 调用真实的注册API
    		$result = $this->callVipRegisterAPI($productType);

    		// 根据结果更新订单状态
    		if($result['code'] == 200){
    			$this->markOrderAsCompleted($orderId, $result);
    		} else {
    			$this->markOrderAsFailed($orderId, $result['message']);
    		}

    		// 返回结果
    		$this->ajaxReturn($result);
    	}

    	/**
    	 * 检查订单支付状态
    	 */
    	private function checkOrderPaymentStatus($orderId){
    		try {
    			$url = "https://cloudshop.qnm6.top/check_payment_status.php?order_id=" . urlencode($orderId);
    			$response = $this->httpGet($url);
    			$data = json_decode($response, true);

    			return isset($data['status']) && $data['status'] === 'success' &&
    				   isset($data['data']['order_status']) && $data['data']['order_status'] === 'paid';
    		} catch (Exception $e) {
    			return false;
    		}
    	}

    	/**
    	 * 调用真实的VIP注册API
    	 */
    	private function callVipRegisterAPI($productType){
    		try {
    			$url = "https://api.qnm6.top/admapi/vipregister.php?lx=" . urlencode($productType);
    			$response = $this->httpGet($url);
    			$data = json_decode($response, true);

    			if($data && isset($data['code'])){
    				return $data;
    			} else {
    				return array('code' => 500, 'message' => '注册服务异常');
    			}
    		} catch (Exception $e) {
    			return array('code' => 500, 'message' => '注册失败：' . $e->getMessage());
    		}
    	}

    	/**
    	 * 标记订单为处理中状态
    	 */
    	private function markOrderAsProcessing($orderId, $productType){
    		$orderModel = M('order_status');
    		$data = array(
    			'order_id' => $orderId,
    			'product_type' => $productType,
    			'status' => 'processing',
    			'created_time' => date('Y-m-d H:i:s'),
    			'updated_time' => date('Y-m-d H:i:s'),
    			'client_ip' => $this->getClientIP()
    		);

    		$existingOrder = $orderModel->where(array('order_id' => $orderId))->find();
    		if($existingOrder){
    			$orderModel->where(array('order_id' => $orderId))->save($data);
    		} else {
    			$orderModel->add($data);
    		}
    	}

    	/**
    	 * 标记订单为完成状态
    	 */
    	private function markOrderAsCompleted($orderId, $result){
    		$orderModel = M('order_status');
    		$data = array(
    			'status' => 'completed',
    			'updated_time' => date('Y-m-d H:i:s'),
    			'result_data' => json_encode($result),
    			'token' => isset($result['token']) ? $result['token'] : '',
    			'reg_time' => isset($result['reg_time']) ? $result['reg_time'] : '',
    			'expire_time' => isset($result['expire_time']) ? $result['expire_time'] : ''
    		);
    		$orderModel->where(array('order_id' => $orderId))->save($data);
    	}

    	/**
    	 * 标记订单为失败状态
    	 */
    	private function markOrderAsFailed($orderId, $errorMessage){
    		$orderModel = M('order_status');
    		$data = array(
    			'status' => 'failed',
    			'updated_time' => date('Y-m-d H:i:s'),
    			'error_message' => $errorMessage
    		);
    		$orderModel->where(array('order_id' => $orderId))->save($data);
    	}

    	/**
    	 * 基础安全检查
    	 */
    	private function securityCheck(){
    		// 检查请求来源
    		$referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
    		$host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '';

    		// 确保请求来自本站
    		if(!empty($referer) && !strpos($referer, $host)){
    			return false;
    		}

    		// 检查User-Agent，防止简单的机器人请求
    		$userAgent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
    		if(empty($userAgent) || strlen($userAgent) < 10){
    			return false;
    		}

    		// 简单的频率限制（基于IP）
    		$clientIP = $this->getClientIP();
    		$cacheKey = 'api_limit_' . md5($clientIP);
    		$requestCount = S($cacheKey);

    		if($requestCount === false){
    			// 第一次请求，设置计数器，5分钟内最多20次请求
    			S($cacheKey, 1, 300);
    		} else if($requestCount >= 20){
    			// 超过限制
    			return false;
    		} else {
    			// 增加计数
    			S($cacheKey, $requestCount + 1, 300);
    		}

    		return true;
    	}

    	/**
    	 * 获取客户端真实IP
    	 */
    	private function getClientIP(){
    		$ip = '';
    		if(!empty($_SERVER['HTTP_X_FORWARDED_FOR'])){
    			$ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
    			$ip = trim($ips[0]);
    		} elseif(!empty($_SERVER['HTTP_X_REAL_IP'])){
    			$ip = $_SERVER['HTTP_X_REAL_IP'];
    		} elseif(!empty($_SERVER['REMOTE_ADDR'])){
    			$ip = $_SERVER['REMOTE_ADDR'];
    		}

    		// 验证IP格式
    		if(filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)){
    			return $ip;
    		}

    		return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    	}

    	/**
    	 * 记录API调用日志
    	 */
    	private function logApiCall($action, $params = array(), $result = ''){
    		$logData = array(
    			'time' => date('Y-m-d H:i:s'),
    			'ip' => $this->getClientIP(),
    			'action' => $action,
    			'params' => json_encode($params),
    			'result' => is_array($result) ? json_encode($result) : $result,
    			'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : ''
    		);

    		// 写入日志文件
    		$logFile = RUNTIME_PATH . 'Logs/api_calls_' . date('Y-m-d') . '.log';
    		$logLine = implode(' | ', $logData) . "\n";
    		file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    	}

    	/**
    	 * HTTP GET请求
    	 */
    	private function httpGet($url, $timeout = 30){
    		// 使用cURL
    		if(function_exists('curl_init')){
    			$ch = curl_init();
    			curl_setopt($ch, CURLOPT_URL, $url);
    			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    			curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    			curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    			curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; VIP-Register-Bot/1.0)');

    			$response = curl_exec($ch);
    			$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    			curl_close($ch);

    			if($httpCode === 200 && $response !== false){
    				return $response;
    			} else {
    				throw new Exception('HTTP请求失败，状态码：' . $httpCode);
    			}
    		}
    		// 备用方案：file_get_contents
    		else if(ini_get('allow_url_fopen')){
    			$context = stream_context_create(array(
    				'http' => array(
    					'timeout' => $timeout,
    					'user_agent' => 'Mozilla/5.0 (compatible; VIP-Register-Bot/1.0)'
    				)
    			));

    			$response = file_get_contents($url, false, $context);
    			if($response !== false){
    				return $response;
    			} else {
    				throw new Exception('HTTP请求失败');
    			}
    		}
    		else {
    			throw new Exception('无可用的HTTP请求方法');
    		}
    	}

}