<?php
require_once 'config.php';

// 设置CORS头
setCORSHeaders();

try {
    // 获取数据库连接
    $pdo = getDBConnection();
    if (!$pdo) {
        jsonResponse([
            'status' => 'error',
            'message' => '数据库连接失败'
        ], 500);
    }
    
    // 创建vip_orders表
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS `vip_orders` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `order_id` varchar(100) NOT NULL COMMENT '订单ID',
          `product_id` int(11) NOT NULL COMMENT '商品ID',
          `product_name` varchar(200) DEFAULT NULL COMMENT '商品名称',
          `product_price` decimal(10,2) DEFAULT NULL COMMENT '商品价格',
          `customer_contact` varchar(200) DEFAULT NULL COMMENT '客户联系方式',
          `pay_type` varchar(20) DEFAULT NULL COMMENT '支付方式',
          `payment_url` text COMMENT '支付链接',
          `order_status` varchar(20) DEFAULT 'unpaid' COMMENT '订单状态：unpaid未支付，paid已支付',
          `is_registered` tinyint(1) DEFAULT 0 COMMENT '是否已注册会员：0未注册，1已注册',
          `member_token` varchar(50) DEFAULT NULL COMMENT '注册的会员Token',
          `member_type` varchar(10) DEFAULT NULL COMMENT '会员类型：1月度，2永久',
          `reg_time` datetime DEFAULT NULL COMMENT '注册时间',
          `expire_time` datetime DEFAULT NULL COMMENT '到期时间',
          `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (`id`),
          UNIQUE KEY `order_id` (`order_id`),
          KEY `idx_order_status` (`order_status`),
          KEY `idx_is_registered` (`is_registered`),
          KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员订单记录表'
    ";
    
    $pdo->exec($createTableSQL);
    
    logMessage("数据库表初始化成功");
    
    jsonResponse([
        'status' => 'success',
        'message' => '数据库初始化成功'
    ]);
    
} catch (Exception $e) {
    logMessage("数据库初始化失败: " . $e->getMessage(), 'ERROR');
    jsonResponse([
        'status' => 'error',
        'message' => '数据库初始化失败: ' . $e->getMessage()
    ], 500);
}
?>
