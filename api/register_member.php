<?php
require_once 'config.php';

// 设置CORS头
setCORSHeaders();

try {
    // 验证必需参数
    validateRequiredParams($_GET, ['order_id', 'lx']);
    
    $orderId = $_GET['order_id'];
    $memberType = $_GET['lx'];
    
    // 验证会员类型
    if (!in_array($memberType, ['1', '2'])) {
        jsonResponse([
            'status' => 'error',
            'message' => '无效的会员类型'
        ], 400);
    }
    
    logMessage("开始注册会员 - 订单ID: {$orderId}, 会员类型: {$memberType}");
    
    // 检查本地数据库中的订单
    $pdo = getDBConnection();
    if (!$pdo) {
        logMessage("数据库连接失败", 'ERROR');
        jsonResponse([
            'status' => 'error',
            'message' => '系统错误，请稍后重试'
        ], 500);
    }
    
    $stmt = $pdo->prepare("SELECT * FROM vip_orders WHERE order_id = ?");
    $stmt->execute([$orderId]);
    $localOrder = $stmt->fetch();
    
    if (!$localOrder) {
        logMessage("本地订单不存在: {$orderId}", 'ERROR');
        jsonResponse([
            'status' => 'error',
            'message' => '订单不存在'
        ], 404);
    }
    
    // 检查订单是否已支付
    if ($localOrder['order_status'] !== 'paid') {
        logMessage("订单未支付，无法注册会员: {$orderId}", 'ERROR');
        jsonResponse([
            'status' => 'error',
            'message' => '订单未支付，无法注册会员'
        ], 400);
    }
    
    // 检查是否已经注册过
    if ($localOrder['is_registered'] == 1) {
        logMessage("订单已注册过会员: {$orderId}");
        jsonResponse([
            'status' => 'already_registered',
            'message' => '此订单已经注册过会员，不能重复注册',
            'data' => [
                'order_id' => $localOrder['order_id'],
                'member_token' => $localOrder['member_token'],
                'reg_time' => $localOrder['reg_time'],
                'expire_time' => $localOrder['expire_time']
            ]
        ]);
    }
    
    // 调用上游API注册会员
    $upstreamParams = ['lx' => $memberType];
    $upstreamResponse = sendHttpRequest(UPSTREAM_REGISTER_MEMBER_URL, $upstreamParams, 'GET');
    
    if ($upstreamResponse === false) {
        logMessage("上游会员注册API调用失败", 'ERROR');
        jsonResponse([
            'status' => 'error',
            'message' => '会员注册失败，请联系客服处理'
        ], 500);
    }
    
    $upstreamData = json_decode($upstreamResponse, true);
    
    if (!$upstreamData || $upstreamData['code'] !== 200) {
        logMessage("上游会员注册API返回错误: " . $upstreamResponse, 'ERROR');
        jsonResponse([
            'status' => 'error',
            'message' => '会员注册失败: ' . ($upstreamData['message'] ?? '未知错误')
        ], 500);
    }
    
    // 更新本地订单记录
    $updateStmt = $pdo->prepare("
        UPDATE vip_orders 
        SET is_registered = 1, 
            member_token = ?, 
            member_type = ?, 
            reg_time = ?, 
            expire_time = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE order_id = ?
    ");
    
    $result = $updateStmt->execute([
        $upstreamData['token'],
        $memberType,
        $upstreamData['reg_time'],
        $upstreamData['expire_time'],
        $orderId
    ]);
    
    if (!$result) {
        logMessage("更新订单注册状态失败: " . implode(', ', $updateStmt->errorInfo()), 'ERROR');
        // 即使更新失败，也返回成功响应，因为会员已经在上游注册成功
    } else {
        logMessage("会员注册成功 - 订单ID: {$orderId}, Token: " . $upstreamData['token']);
    }
    
    // 返回成功响应
    jsonResponse([
        'status' => 'success',
        'message' => '会员注册成功',
        'data' => [
            'order_id' => $orderId,
            'token' => $upstreamData['token'],
            'reg_time' => $upstreamData['reg_time'],
            'expire_time' => $upstreamData['expire_time']
        ]
    ]);
    
} catch (Exception $e) {
    logMessage("注册会员异常: " . $e->getMessage(), 'ERROR');
    jsonResponse([
        'status' => 'error',
        'message' => '系统错误，请联系客服处理'
    ], 500);
}
?>
