<?php
require_once 'config.php';

// 设置CORS头
setCORSHeaders();

try {
    // 验证必需参数
    validateRequiredParams($_GET, ['order_id']);
    
    $orderId = $_GET['order_id'];
    
    logMessage("开始检查支付状态 - 订单ID: {$orderId}");
    
    // 先检查本地数据库中的订单状态
    $pdo = getDBConnection();
    if (!$pdo) {
        logMessage("数据库连接失败", 'ERROR');
        jsonResponse([
            'status' => 'error',
            'message' => '系统错误，请稍后重试'
        ], 500);
    }
    
    $stmt = $pdo->prepare("SELECT * FROM vip_orders WHERE order_id = ?");
    $stmt->execute([$orderId]);
    $localOrder = $stmt->fetch();
    
    if (!$localOrder) {
        logMessage("本地订单不存在: {$orderId}", 'ERROR');
        jsonResponse([
            'status' => 'error',
            'message' => '订单不存在'
        ], 404);
    }
    
    // 如果本地记录显示已支付，直接返回
    if ($localOrder['order_status'] === 'paid') {
        logMessage("本地订单已标记为已支付: {$orderId}");
        jsonResponse([
            'status' => 'success',
            'message' => '订单已支付',
            'data' => [
                'order_id' => $localOrder['order_id'],
                'order_status' => 'paid',
                'product_name' => $localOrder['product_name'],
                'product_price' => $localOrder['product_price'],
                'customer_contact' => $localOrder['customer_contact'],
                'created_at' => $localOrder['created_at'],
                'updated_at' => $localOrder['updated_at']
            ]
        ]);
    }
    
    // 调用上游API检查支付状态
    $upstreamParams = ['order_id' => $orderId];
    $upstreamResponse = sendHttpRequest(UPSTREAM_CHECK_PAYMENT_URL, $upstreamParams, 'GET');
    
    if ($upstreamResponse === false) {
        logMessage("上游支付状态检查API调用失败", 'ERROR');
        jsonResponse([
            'status' => 'error',
            'message' => '支付状态检查失败，请稍后重试'
        ], 500);
    }
    
    $upstreamData = json_decode($upstreamResponse, true);
    
    if (!$upstreamData || $upstreamData['status'] !== 'success') {
        logMessage("上游支付状态检查API返回错误: " . $upstreamResponse, 'ERROR');
        jsonResponse([
            'status' => 'error',
            'message' => '支付状态检查失败: ' . ($upstreamData['message'] ?? '未知错误')
        ], 500);
    }
    
    // 更新本地订单状态
    if (isset($upstreamData['data']['order_status'])) {
        $newStatus = $upstreamData['data']['order_status'];
        
        $updateStmt = $pdo->prepare("
            UPDATE vip_orders 
            SET order_status = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE order_id = ?
        ");
        $updateStmt->execute([$newStatus, $orderId]);
        
        logMessage("更新本地订单状态: {$orderId} -> {$newStatus}");
    }
    
    // 返回响应
    if ($upstreamData['data']['order_status'] === 'paid') {
        logMessage("订单支付成功: {$orderId}");
        jsonResponse([
            'status' => 'success',
            'message' => '支付成功，订单已更新',
            'data' => $upstreamData['data']
        ]);
    } else {
        logMessage("订单未支付: {$orderId}");
        jsonResponse([
            'status' => 'success',
            'message' => '订单未支付',
            'data' => [
                'order_id' => $orderId,
                'order_status' => 'unpaid',
                'delivery_content' => '*******'
            ]
        ]);
    }
    
} catch (Exception $e) {
    logMessage("检查支付状态异常: " . $e->getMessage(), 'ERROR');
    jsonResponse([
        'status' => 'error',
        'message' => '系统错误，请稍后重试'
    ], 500);
}
?>
