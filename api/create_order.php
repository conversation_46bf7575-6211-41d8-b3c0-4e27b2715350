<?php
require_once 'config.php';

// 设置CORS头
setCORSHeaders();

try {
    // 验证必需参数
    validateRequiredParams($_GET, ['customer_contact', 'product_id']);
    
    $customerContact = $_GET['customer_contact'];
    $productId = intval($_GET['product_id']);
    $payType = $_GET['pay_type'] ?? 'wxpay';
    
    // 验证商品ID
    if (!in_array($productId, [85, 86])) {
        jsonResponse([
            'status' => 'error',
            'message' => '无效的商品ID'
        ], 400);
    }
    
    // 验证支付方式
    if (!in_array($payType, ['wxpay', 'alipay'])) {
        jsonResponse([
            'status' => 'error',
            'message' => '无效的支付方式'
        ], 400);
    }
    
    logMessage("开始创建订单 - 商品ID: {$productId}, 支付方式: {$payType}");
    
    // 调用上游API创建订单
    $upstreamParams = [
        'customer_contact' => $customerContact,
        'product_id' => $productId,
        'pay_type' => $payType
    ];
    
    $upstreamResponse = sendHttpRequest(UPSTREAM_CREATE_ORDER_URL, $upstreamParams, 'GET');
    
    if ($upstreamResponse === false) {
        logMessage("上游API调用失败", 'ERROR');
        jsonResponse([
            'status' => 'error',
            'message' => '订单创建失败，请稍后重试'
        ], 500);
    }
    
    $upstreamData = json_decode($upstreamResponse, true);
    
    if (!$upstreamData || $upstreamData['status'] !== 'success') {
        logMessage("上游API返回错误: " . $upstreamResponse, 'ERROR');
        jsonResponse([
            'status' => 'error',
            'message' => '订单创建失败: ' . ($upstreamData['message'] ?? '未知错误')
        ], 500);
    }
    
    // 保存订单到本地数据库
    $pdo = getDBConnection();
    if (!$pdo) {
        logMessage("数据库连接失败", 'ERROR');
        jsonResponse([
            'status' => 'error',
            'message' => '系统错误，请稍后重试'
        ], 500);
    }
    
    $orderInfo = $upstreamData['data']['order_info'];
    
    $stmt = $pdo->prepare("
        INSERT INTO vip_orders (
            order_id, product_id, product_name, product_price, 
            customer_contact, pay_type, payment_url, order_status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            product_name = VALUES(product_name),
            product_price = VALUES(product_price),
            customer_contact = VALUES(customer_contact),
            pay_type = VALUES(pay_type),
            payment_url = VALUES(payment_url),
            order_status = VALUES(order_status),
            updated_at = CURRENT_TIMESTAMP
    ");
    
    $result = $stmt->execute([
        $orderInfo['order_id'],
        $productId,
        $orderInfo['product_name'],
        $orderInfo['product_price'],
        $customerContact,
        $payType,
        $upstreamData['data']['payment_url'],
        $orderInfo['order_status']
    ]);
    
    if (!$result) {
        logMessage("订单保存失败: " . implode(', ', $stmt->errorInfo()), 'ERROR');
        // 即使保存失败，也返回上游的成功响应，因为订单已经在上游创建成功
    } else {
        logMessage("订单保存成功 - 订单ID: " . $orderInfo['order_id']);
    }
    
    // 返回成功响应
    jsonResponse([
        'status' => 'success',
        'message' => '订单创建成功',
        'data' => $upstreamData['data']
    ]);
    
} catch (Exception $e) {
    logMessage("创建订单异常: " . $e->getMessage(), 'ERROR');
    jsonResponse([
        'status' => 'error',
        'message' => '系统错误，请稍后重试'
    ], 500);
}
?>
