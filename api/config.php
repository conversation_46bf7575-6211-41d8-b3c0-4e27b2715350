<?php
// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'sql_5432_com');
define('DB_USER', 'root');
define('DB_PASS', '');

// 上游API配置
define('UPSTREAM_CREATE_ORDER_URL', 'https://cloudshop.qnm6.top/create_order.php');
define('UPSTREAM_CHECK_PAYMENT_URL', 'https://cloudshop.qnm6.top/check_payment_status.php');
define('UPSTREAM_REGISTER_MEMBER_URL', 'https://api.qnm6.top/admapi/vipregister.php');

// 数据库连接函数
function getDBConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        error_log("数据库连接失败: " . $e->getMessage());
        return null;
    }
}

// 设置CORS头
function setCORSHeaders() {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    header('Content-Type: application/json; charset=utf-8');
    
    // 处理预检请求
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
}

// 返回JSON响应
function jsonResponse($data, $httpCode = 200) {
    http_response_code($httpCode);
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit();
}

// 记录日志
function logMessage($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    error_log($logMessage, 3, __DIR__ . '/../logs/vip_api.log');
}

// 发送HTTP请求
function sendHttpRequest($url, $params = [], $method = 'GET') {
    $ch = curl_init();
    
    if ($method === 'GET' && !empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_USERAGENT => 'VIP-API-Client/1.0',
    ]);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        logMessage("HTTP请求失败: {$error}", 'ERROR');
        return false;
    }
    
    if ($httpCode !== 200) {
        logMessage("HTTP请求返回错误码: {$httpCode}", 'ERROR');
        return false;
    }
    
    return $response;
}

// 验证必需参数
function validateRequiredParams($params, $required) {
    $missing = [];
    foreach ($required as $param) {
        if (!isset($params[$param]) || empty($params[$param])) {
            $missing[] = $param;
        }
    }
    
    if (!empty($missing)) {
        jsonResponse([
            'status' => 'error',
            'message' => '缺少必需参数: ' . implode(', ', $missing)
        ], 400);
    }
}

// 创建日志目录
$logDir = __DIR__ . '/../logs';
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}
?>
