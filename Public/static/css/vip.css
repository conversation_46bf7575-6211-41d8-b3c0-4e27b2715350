/* 像素风格会员购买页面样式 */
@import url('https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap');

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Press Start 2P', monospace;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 50%, #fff3e0 100%);
    min-height: 100vh;
    font-size: 12px;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px 20px;
    background: linear-gradient(45deg, #2196f3, #9c27b0);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect width="10" height="10" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    background-size: 20px 20px;
}

.header-content {
    position: relative;
    z-index: 1;
}

.title {
    color: white;
    font-size: 24px;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    color: rgba(255,255,255,0.9);
    font-size: 10px;
    margin-bottom: 20px;
}

.home-btn {
    background: #ff9800;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-family: inherit;
    font-size: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255,152,0,0.3);
}

.home-btn:hover {
    background: #f57c00;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255,152,0,0.4);
}

/* 主要内容区域 */
.main-content {
    margin-bottom: 40px;
}

/* 套餐卡片容器 */
.plans-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

/* 套餐卡片 */
.plan-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 3px solid transparent;
}

.plan-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
    opacity: 0.1;
    z-index: 0;
}

.plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.plan-card.popular {
    border-color: #9c27b0;
    transform: scale(1.05);
}

.plan-card.popular::before {
    background: linear-gradient(45deg, #9c27b0, #2196f3);
    opacity: 0.1;
}

/* 套餐头部 */
.plan-header {
    text-align: center;
    margin-bottom: 20px;
    position: relative;
    z-index: 1;
}

.plan-icon {
    font-size: 32px;
    margin-bottom: 10px;
}

.plan-title {
    font-size: 16px;
    color: #333;
    margin-bottom: 10px;
}

.plan-badge {
    display: inline-block;
    background: #4caf50;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 8px;
}

.popular-badge {
    background: #9c27b0;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* 套餐内容 */
.plan-content {
    position: relative;
    z-index: 1;
}

.price-section {
    text-align: center;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(33,150,243,0.1);
    border-radius: 10px;
}

.price {
    font-size: 20px;
    color: #2196f3;
    display: block;
    margin-bottom: 5px;
}

.duration {
    font-size: 10px;
    color: #666;
}

/* 功能列表 */
.features {
    list-style: none;
    margin-bottom: 20px;
}

.features li {
    padding: 8px 0;
    font-size: 10px;
    color: #555;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.features li:last-child {
    border-bottom: none;
}

/* 选择按钮 */
.select-btn {
    width: 100%;
    background: linear-gradient(45deg, #2196f3, #21cbf3);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 10px;
    cursor: pointer;
    font-family: inherit;
    font-size: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(33,150,243,0.3);
}

.select-btn:hover {
    background: linear-gradient(45deg, #1976d2, #00bcd4);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(33,150,243,0.4);
}

.popular .select-btn {
    background: linear-gradient(45deg, #9c27b0, #e91e63);
    box-shadow: 0 4px 15px rgba(156,39,176,0.3);
}

.popular .select-btn:hover {
    background: linear-gradient(45deg, #7b1fa2, #c2185b);
    box-shadow: 0 6px 20px rgba(156,39,176,0.4);
}

/* 客服支持区域 */
.support-section {
    margin-top: 40px;
}

.support-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    border: 2px solid #ff9800;
}

.support-card h4 {
    color: #ff9800;
    font-size: 14px;
    margin-bottom: 15px;
}

.support-card p {
    color: #666;
    font-size: 10px;
    margin-bottom: 20px;
    line-height: 1.8;
}

.support-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.support-btn {
    display: inline-block;
    padding: 12px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-family: inherit;
    font-size: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.support-btn.telegram {
    background: #0088cc;
    color: white;
}

.support-btn.telegram:hover {
    background: #006699;
    transform: translateY(-2px);
}

.support-btn.complaint {
    background: #f44336;
    color: white;
}

.support-btn.complaint:hover {
    background: #d32f2f;
    transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .title {
        font-size: 18px;
    }
    
    .plans-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .plan-card.popular {
        transform: none;
    }
    
    .support-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .support-btn {
        width: 200px;
    }
}

@media (max-width: 480px) {
    body {
        font-size: 10px;
    }
    
    .title {
        font-size: 16px;
    }
    
    .plan-card {
        padding: 20px;
    }
    
    .price {
        font-size: 18px;
    }
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease-out;
    overflow: hidden;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background: linear-gradient(45deg, #2196f3, #9c27b0);
    color: white;
    padding: 20px;
    text-align: center;
}

.modal-header h3 {
    font-size: 14px;
    margin: 0;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #eee;
    text-align: center;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-family: inherit;
    font-size: 10px;
    transition: all 0.3s ease;
    margin: 0 5px;
}

.btn-primary {
    background: linear-gradient(45deg, #2196f3, #21cbf3);
    color: white;
    box-shadow: 0 4px 15px rgba(33,150,243,0.3);
}

.btn-primary:hover {
    background: linear-gradient(45deg, #1976d2, #00bcd4);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(33,150,243,0.4);
}

.btn-secondary {
    background: #f5f5f5;
    color: #666;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn-secondary:hover {
    background: #e0e0e0;
    transform: translateY(-2px);
}

/* 套餐信息显示 */
.selected-plan-info, .order-info, .success-info {
    background: rgba(33,150,243,0.1);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.plan-info-item, .order-item, .success-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.plan-info-item:last-child, .order-item:last-child, .success-item:last-child {
    border-bottom: none;
}

.label {
    font-size: 10px;
    color: #666;
    flex-shrink: 0;
}

.value {
    font-size: 10px;
    color: #333;
    font-weight: bold;
    margin-right: 10px;
    word-break: break-all;
}

.success-item.highlight .value {
    color: #9c27b0;
    background: rgba(156,39,176,0.1);
    padding: 4px 8px;
    border-radius: 4px;
}

/* 复制按钮 */
.copy-btn {
    background: #4caf50;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 8px;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.copy-btn:hover {
    background: #45a049;
    transform: scale(1.1);
}

/* 支付方式选择 */
.payment-methods {
    margin-bottom: 20px;
}

.payment-method {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-method:hover {
    border-color: #2196f3;
    background: rgba(33,150,243,0.05);
}

.payment-method.selected {
    border-color: #2196f3;
    background: rgba(33,150,243,0.1);
}

.payment-icon {
    font-size: 24px;
    margin-right: 15px;
}

.payment-info {
    flex: 1;
}

.payment-info h4 {
    font-size: 12px;
    color: #333;
    margin-bottom: 5px;
}

.payment-info p {
    font-size: 8px;
    color: #666;
}

.payment-radio {
    position: relative;
}

.payment-radio input[type="radio"] {
    display: none;
}

.payment-radio label {
    display: block;
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 50%;
    cursor: pointer;
    position: relative;
}

.payment-radio input[type="radio"]:checked + label {
    border-color: #2196f3;
}

.payment-radio input[type="radio"]:checked + label::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 10px;
    height: 10px;
    background: #2196f3;
    border-radius: 50%;
}

/* 二维码容器 */
.qrcode-container {
    text-align: center;
    margin: 20px 0;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

#qrcode {
    display: inline-block;
    margin-bottom: 15px;
    padding: 10px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.qr-tip {
    font-size: 10px;
    color: #666;
    margin: 0;
}

#payTypeName {
    color: #2196f3;
    font-weight: bold;
}

/* 支付错误提示 */
.payment-error {
    background: #ffebee;
    border: 2px solid #f44336;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    text-align: center;
}

.payment-error p {
    color: #f44336;
    font-size: 10px;
    margin: 0;
    font-weight: bold;
}

/* 重要提醒 */
.important-notice {
    background: #fff3e0;
    border: 2px solid #ff9800;
    border-radius: 10px;
    padding: 15px;
    margin: 20px 0;
}

.important-notice h4 {
    color: #ff9800;
    font-size: 12px;
    margin-bottom: 10px;
}

.important-notice p {
    color: #666;
    font-size: 10px;
    line-height: 1.6;
    margin: 0;
}

/* 支持提醒 */
.support-notice {
    background: rgba(76,175,80,0.1);
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    text-align: center;
}

.support-notice p {
    font-size: 10px;
    color: #666;
    margin-bottom: 15px;
}

/* 加载中模态框 */
.loading-content {
    text-align: center;
    padding: 40px 20px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2196f3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#loadingText {
    font-size: 10px;
    color: #666;
    margin: 0;
}

/* 移动端模态框优化 */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .modal-body {
        padding: 20px;
    }

    .payment-method {
        padding: 12px;
    }

    .payment-icon {
        font-size: 20px;
        margin-right: 10px;
    }

    .support-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .support-btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .modal-content {
        margin: 5% auto;
    }

    .modal-header {
        padding: 15px;
    }

    .modal-header h3 {
        font-size: 12px;
    }

    .modal-body {
        padding: 15px;
    }

    .btn {
        padding: 10px 20px;
        font-size: 9px;
    }

    .value {
        font-size: 9px;
    }

    .label {
        font-size: 9px;
    }
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 8px;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* 成功动画 */
.success-animation {
    animation: successPulse 0.6s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* 防止模态框点击关闭 */
.modal-open {
    overflow: hidden;
}

.modal-content {
    pointer-events: auto;
}

.modal {
    pointer-events: none;
}

/* 安抚用户的样式 */
.comfort-message {
    background: linear-gradient(45deg, #e8f5e8, #f0f8ff);
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
    text-align: center;
}

.comfort-message .icon {
    font-size: 24px;
    margin-bottom: 10px;
    display: block;
}

.comfort-message p {
    color: #2e7d32;
    font-size: 10px;
    line-height: 1.6;
    margin: 5px 0;
}

.comfort-message .highlight {
    color: #1976d2;
    font-weight: bold;
}

/* 紧急联系样式 */
.emergency-contact {
    background: #fff3e0;
    border: 2px dashed #ff9800;
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
    text-align: center;
    animation: gentlePulse 3s infinite;
}

@keyframes gentlePulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.emergency-contact h4 {
    color: #f57c00;
    font-size: 12px;
    margin-bottom: 10px;
}

.emergency-contact p {
    color: #bf360c;
    font-size: 10px;
    line-height: 1.8;
    margin: 5px 0;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.pending {
    background: #ff9800;
    animation: blink 1.5s infinite;
}

.status-indicator.success {
    background: #4caf50;
}

.status-indicator.error {
    background: #f44336;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
    overflow: hidden;
    margin: 15px 0;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(45deg, #2196f3, #21cbf3);
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* 步骤指示器 */
.steps-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
}

.step {
    display: flex;
    align-items: center;
    font-size: 10px;
    color: #999;
}

.step.active {
    color: #2196f3;
}

.step.completed {
    color: #4caf50;
}

.step-number {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #e0e0e0;
    color: white;
    text-align: center;
    line-height: 20px;
    margin-right: 8px;
    font-size: 8px;
}

.step.active .step-number {
    background: #2196f3;
}

.step.completed .step-number {
    background: #4caf50;
}

.step-connector {
    width: 30px;
    height: 2px;
    background: #e0e0e0;
    margin: 0 10px;
}

.step.completed + .step .step-connector {
    background: #4caf50;
}
