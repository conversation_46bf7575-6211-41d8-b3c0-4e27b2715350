// 会员购买页面JavaScript逻辑
let currentOrder = null;
let paymentCheckInterval = null;

// 页面加载完成后初始化
$(document).ready(function() {
    initializeEventListeners();
});

// 初始化事件监听器
function initializeEventListeners() {
    // 支付方式选择
    $('.payment-method').click(function() {
        $('.payment-method').removeClass('selected');
        $(this).addClass('selected');
        $(this).find('input[type="radio"]').prop('checked', true);
    });
    
    // 阻止模态框点击关闭
    $('.modal').click(function(e) {
        if (e.target === this) {
            e.preventDefault();
            return false;
        }
    });
}

// 返回首页
function goHome() {
    window.location.href = '/';
}

// 选择套餐
function selectPlan(productId, price, planName, planType) {
    // 显示支付方式选择模态框
    $('#selectedPlanName').text(planName);
    $('#selectedPlanPrice').text('¥' + price);
    
    // 存储选择的套餐信息
    window.selectedPlan = {
        productId: productId,
        price: price,
        planName: planName,
        planType: planType
    };
    
    showModal('paymentModal');
}

// 关闭支付方式选择模态框
function closePaymentModal() {
    hideModal('paymentModal');
    window.selectedPlan = null;
}

// 创建订单
function createOrder() {
    if (!window.selectedPlan) {
        showError('请先选择套餐');
        return;
    }
    
    const payType = $('input[name="payType"]:checked').val();
    const customerContact = generateCustomerContact();
    
    showLoading('正在创建订单...');
    
    // 调用代理API创建订单
    $.ajax({
        url: 'api/create_order.php',
        method: 'GET',
        data: {
            customer_contact: customerContact,
            product_id: window.selectedPlan.productId,
            pay_type: payType
        },
        success: function(response) {
            hideLoading();
            if (response.status === 'success') {
                currentOrder = response.data;
                showPaymentQRCode(response.data, payType);
                hideModal('paymentModal');
            } else {
                showError('订单创建失败：' + response.message);
            }
        },
        error: function(xhr, status, error) {
            hideLoading();
            handleApiError(xhr, '网络错误，请稍后重试');
            console.error('创建订单失败:', error);
        }
    });
}

// 显示支付二维码
function showPaymentQRCode(orderData, payType) {
    // 填充订单信息
    $('#orderProductName').text(orderData.order_info.product_name);
    $('#orderTime').text(new Date().toLocaleString('zh-CN'));
    $('#orderNumber').text(orderData.order_info.order_id);
    
    // 设置支付方式名称
    const payTypeName = payType === 'wxpay' ? '微信' : '支付宝';
    $('#payTypeName').text(payTypeName);
    
    // 生成二维码
    generateQRCode(orderData.payment_url);
    
    // 隐藏支付错误提示
    $('#paymentError').hide();
    
    // 显示二维码模态框
    showModal('qrcodeModal');
}

// 生成二维码
function generateQRCode(url) {
    // 清空之前的二维码
    $('#qrcode').empty();
    
    // 使用qrcode.js生成二维码
    const qr = qrcode(0, 'M');
    qr.addData(url);
    qr.make();
    
    // 创建二维码图片
    const qrImage = qr.createImgTag(4, 8);
    $('#qrcode').html(qrImage);
}

// 检查支付状态
function checkPaymentStatus() {
    if (!currentOrder) {
        showError('订单信息丢失，请重新下单');
        return;
    }
    
    showLoading('正在检查支付状态...');
    
    $.ajax({
        url: 'api/check_payment.php',
        method: 'GET',
        data: {
            order_id: currentOrder.order_info.order_id
        },
        success: function(response) {
            hideLoading();
            if (response.status === 'success') {
                if (response.data.order_status === 'paid') {
                    // 支付成功，注册会员
                    registerMember();
                } else {
                    // 未支付
                    $('#paymentError').show();
                    setTimeout(function() {
                        $('#paymentError').hide();
                    }, 3000);
                }
            } else {
                showError('检查支付状态失败：' + response.message);
            }
        },
        error: function(xhr, status, error) {
            hideLoading();
            handleApiError(xhr, '网络错误，请稍后重试');
            console.error('检查支付状态失败:', error);
        }
    });
}

// 注册会员
function registerMember() {
    if (!currentOrder || !window.selectedPlan) {
        showError('订单信息丢失');
        return;
    }
    
    showLoading('正在注册会员...');
    
    const memberType = window.selectedPlan.planType === 'monthly' ? '1' : '2';
    
    $.ajax({
        url: 'api/register_member.php',
        method: 'GET',
        data: {
            order_id: currentOrder.order_info.order_id,
            lx: memberType
        },
        success: function(response) {
            hideLoading();
            if (response.status === 'success') {
                // 注册成功
                showSuccessModal(response.data);
                hideModal('qrcodeModal');
            } else if (response.status === 'already_registered') {
                showError('此订单已经注册过会员，不能重复注册');
            } else {
                showError('注册失败：' + response.message + '，请联系客服处理');
            }
        },
        error: function(xhr, status, error) {
            hideLoading();
            handleApiError(xhr, '网络错误，请联系客服处理');
            console.error('注册会员失败:', error);
        }
    });
}

// 显示注册成功模态框
function showSuccessModal(memberData) {
    $('#regTime').text(memberData.reg_time);
    $('#expireTime').text(memberData.expire_time);
    $('#memberToken').text(memberData.token);
    
    showModal('successModal');
    
    // 添加成功动画
    $('.modal-content').addClass('success-animation');
    setTimeout(function() {
        $('.modal-content').removeClass('success-animation');
    }, 600);
}

// 关闭成功模态框
function closeSuccessModal() {
    hideModal('successModal');
    // 清理订单信息
    currentOrder = null;
    window.selectedPlan = null;
}

// 复制文本到剪贴板
function copyText(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent || element.innerText;
    
    if (navigator.clipboard && window.isSecureContext) {
        // 现代浏览器
        navigator.clipboard.writeText(text).then(function() {
            showToast('复制成功');
        }).catch(function(err) {
            fallbackCopyText(text);
        });
    } else {
        // 兼容旧浏览器
        fallbackCopyText(text);
    }
}

// 兼容性复制方法
function fallbackCopyText(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showToast('复制成功');
    } catch (err) {
        showToast('复制失败，请手动复制');
    }
    
    document.body.removeChild(textArea);
}

// 生成客户联系方式（简单的时间戳）
function generateCustomerContact() {
    return 'user_' + Date.now() + '@temp.com';
}

// 显示模态框
function showModal(modalId) {
    $('#' + modalId).fadeIn(300);
    $('body').addClass('modal-open');
}

// 隐藏模态框
function hideModal(modalId) {
    $('#' + modalId).fadeOut(300);
    $('body').removeClass('modal-open');
}

// 显示加载中
function showLoading(text) {
    $('#loadingText').text(text || '正在处理中...');
    showModal('loadingModal');
}

// 隐藏加载中
function hideLoading() {
    hideModal('loadingModal');
}

// 显示错误信息
function showError(message) {
    layer.msg(message, {
        icon: 2,
        time: 3000,
        skin: 'layer-ext-moon'
    });
}

// 显示成功提示
function showToast(message) {
    layer.msg(message, {
        icon: 1,
        time: 2000,
        skin: 'layer-ext-moon'
    });
}

// 显示安抚用户的消息
function showComfortMessage(container, message, type = 'info') {
    const comfortHtml = `
        <div class="comfort-message">
            <span class="icon">${type === 'info' ? '💡' : type === 'warning' ? '⚠️' : '✅'}</span>
            <p>${message}</p>
            <p class="highlight">如有任何问题，请优先联系客服处理！</p>
        </div>
    `;
    $(container).append(comfortHtml);
}

// 显示紧急联系信息
function showEmergencyContact(container) {
    const emergencyHtml = `
        <div class="emergency-contact">
            <h4>🆘 遇到问题？</h4>
            <p>请不要直接投诉到支付平台！</p>
            <p>我们的客服会第一时间为您解决问题</p>
            <div class="support-buttons" style="margin-top: 15px;">
                <a href="https://t.me/Dataso" target="_blank" class="support-btn telegram">
                    📱 立即联系客服
                </a>
                <a href="https://cloudshop.qnm6.top/tousu.html" target="_blank" class="support-btn complaint">
                    📝 投诉订单
                </a>
            </div>
        </div>
    `;
    $(container).append(emergencyHtml);
}

// 更新步骤指示器
function updateStepsIndicator(currentStep) {
    const steps = ['选择套餐', '确认支付', '扫码付款', '注册成功'];
    const stepsHtml = steps.map((step, index) => {
        let stepClass = '';
        if (index < currentStep) stepClass = 'completed';
        else if (index === currentStep) stepClass = 'active';

        const connector = index < steps.length - 1 ? '<div class="step-connector"></div>' : '';

        return `
            <div class="step ${stepClass}">
                <span class="step-number">${index + 1}</span>
                <span class="step-text">${step}</span>
            </div>
            ${connector}
        `;
    }).join('');

    return `<div class="steps-indicator">${stepsHtml}</div>`;
}

// 增强的错误处理
function handleApiError(xhr, defaultMessage) {
    let errorMessage = defaultMessage;

    try {
        const response = JSON.parse(xhr.responseText);
        if (response.message) {
            errorMessage = response.message;
        }
    } catch (e) {
        // 使用默认消息
    }

    // 显示错误信息
    showError(errorMessage);

    // 显示安抚消息
    showComfortMessage('.modal-body:visible', '系统可能暂时繁忙，请稍后重试或联系客服', 'warning');
}

// 页面初始化时显示欢迎信息
function showWelcomeMessage() {
    const welcomeHtml = `
        <div class="comfort-message" style="margin-bottom: 30px;">
            <span class="icon">🎉</span>
            <p>欢迎来到会员购买中心！</p>
            <p>我们提供安全可靠的支付服务，如有任何疑问请随时联系客服</p>
        </div>
    `;
    $('.main-content').prepend(welcomeHtml);
}

// 页面加载完成后的增强初始化
$(document).ready(function() {
    initializeEventListeners();
    showWelcomeMessage();

    // 初始化数据库（可选）
    $.get('api/init_database.php').done(function(response) {
        console.log('数据库初始化:', response);
    }).fail(function() {
        console.log('数据库初始化失败，但不影响正常使用');
    });
});
