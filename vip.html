<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员购买 - 5432工具箱</title>
    <link rel="stylesheet" href="Public/static/css/vip.css">
    <script src="Public/js/jquery-1.9.1.min.js"></script>
    <script src="Public/js/qrcode.js"></script>
    <script src="Public/layer/layer.js"></script>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <h1 class="title">🎮 会员购买中心</h1>
                <p class="subtitle">解锁全部功能，享受极致体验</p>
                <button class="home-btn" onclick="goHome()">🏠 返回首页</button>
            </div>
        </header>

        <!-- 会员套餐选择 -->
        <main class="main-content">
            <div class="plans-container">
                <div class="plan-card" data-product-id="85" data-price="28.05" data-type="monthly">
                    <div class="plan-header">
                        <div class="plan-icon">📅</div>
                        <h3 class="plan-title">月度会员</h3>
                        <div class="plan-badge">买一月送一周</div>
                    </div>
                    <div class="plan-content">
                        <div class="price-section">
                            <span class="price">¥28.05</span>
                            <span class="duration">38天</span>
                        </div>
                        <ul class="features">
                            <li>✨ 解锁全部工具</li>
                            <li>🚀 无限制使用</li>
                            <li>💎 专属会员标识</li>
                            <li>🎁 额外赠送7天</li>
                        </ul>
                        <button class="select-btn" onclick="selectPlan(85, '28.05', '月度会员', 'monthly')">
                            选择此套餐
                        </button>
                    </div>
                </div>

                <div class="plan-card popular" data-product-id="86" data-price="38.05" data-type="lifetime">
                    <div class="plan-header">
                        <div class="plan-icon">👑</div>
                        <h3 class="plan-title">永久会员</h3>
                        <div class="plan-badge popular-badge">推荐</div>
                    </div>
                    <div class="plan-content">
                        <div class="price-section">
                            <span class="price">¥38.05</span>
                            <span class="duration">永久</span>
                        </div>
                        <ul class="features">
                            <li>✨ 解锁全部工具</li>
                            <li>🚀 无限制使用</li>
                            <li>💎 专属会员标识</li>
                            <li>🎯 终身免费更新</li>
                            <li>⭐ 永不到期</li>
                        </ul>
                        <button class="select-btn" onclick="selectPlan(86, '38.05', '永久会员', 'lifetime')">
                            选择此套餐
                        </button>
                    </div>
                </div>
            </div>

            <!-- 客服和投诉入口 -->
            <div class="support-section">
                <div class="support-card">
                    <h4>💬 需要帮助？</h4>
                    <p>如有任何问题，请优先联系客服或投诉订单，避免直接投诉到支付平台！</p>
                    <div class="support-buttons">
                        <a href="https://t.me/Dataso" target="_blank" class="support-btn telegram">
                            📱 联系客服
                        </a>
                        <a href="https://cloudshop.qnm6.top/tousu.html" target="_blank" class="support-btn complaint">
                            📝 投诉订单
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 支付方式选择模态框 -->
    <div id="paymentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>选择支付方式</h3>
            </div>
            <div class="modal-body">
                <div class="selected-plan-info">
                    <div class="plan-info-item">
                        <span class="label">商品：</span>
                        <span id="selectedPlanName" class="value"></span>
                    </div>
                    <div class="plan-info-item">
                        <span class="label">价格：</span>
                        <span id="selectedPlanPrice" class="value"></span>
                    </div>
                </div>
                
                <div class="payment-methods">
                    <div class="payment-method" data-type="wxpay">
                        <div class="payment-icon">💚</div>
                        <div class="payment-info">
                            <h4>微信支付</h4>
                            <p>使用微信扫码支付</p>
                        </div>
                        <div class="payment-radio">
                            <input type="radio" name="payType" value="wxpay" id="wxpay" checked>
                            <label for="wxpay"></label>
                        </div>
                    </div>
                    
                    <div class="payment-method" data-type="alipay">
                        <div class="payment-icon">💙</div>
                        <div class="payment-info">
                            <h4>支付宝</h4>
                            <p>使用支付宝扫码支付</p>
                        </div>
                        <div class="payment-radio">
                            <input type="radio" name="payType" value="alipay" id="alipay">
                            <label for="alipay"></label>
                        </div>
                    </div>
                </div>
                
                <div class="support-notice">
                    <p>⚠️ 如遇支付问题，请优先联系客服处理，避免直接投诉到支付平台！</p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closePaymentModal()">取消</button>
                <button class="btn btn-primary" onclick="createOrder()">确认支付</button>
            </div>
        </div>
    </div>

    <!-- 支付二维码模态框 -->
    <div id="qrcodeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>扫码支付</h3>
            </div>
            <div class="modal-body">
                <div class="order-info">
                    <div class="order-item">
                        <span class="label">商品名称：</span>
                        <span id="orderProductName" class="value"></span>
                        <button class="copy-btn" onclick="copyText('orderProductName')">📋</button>
                    </div>
                    <div class="order-item">
                        <span class="label">下单时间：</span>
                        <span id="orderTime" class="value"></span>
                        <button class="copy-btn" onclick="copyText('orderTime')">📋</button>
                    </div>
                    <div class="order-item">
                        <span class="label">订单号：</span>
                        <span id="orderNumber" class="value"></span>
                        <button class="copy-btn" onclick="copyText('orderNumber')">📋</button>
                    </div>
                </div>

                <div class="qrcode-container">
                    <div id="qrcode"></div>
                    <p class="qr-tip">请使用<span id="payTypeName">微信</span>扫码支付</p>
                </div>

                <div id="paymentError" class="payment-error" style="display: none;">
                    <p>❌ 未检测到支付，请先完成支付！</p>
                </div>

                <div class="support-notice">
                    <p>💡 支付遇到问题？请联系客服获取帮助</p>
                    <div class="support-buttons">
                        <a href="https://t.me/Dataso" target="_blank" class="support-btn telegram">
                            📱 联系客服
                        </a>
                        <a href="https://cloudshop.qnm6.top/tousu.html" target="_blank" class="support-btn complaint">
                            📝 投诉订单
                        </a>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="checkPaymentStatus()">我已支付</button>
            </div>
        </div>
    </div>

    <!-- 注册成功模态框 -->
    <div id="successModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🎉 注册成功</h3>
            </div>
            <div class="modal-body">
                <div class="success-info">
                    <div class="success-item">
                        <span class="label">注册时间：</span>
                        <span id="regTime" class="value"></span>
                        <button class="copy-btn" onclick="copyText('regTime')">📋</button>
                    </div>
                    <div class="success-item">
                        <span class="label">到期时间：</span>
                        <span id="expireTime" class="value"></span>
                        <button class="copy-btn" onclick="copyText('expireTime')">📋</button>
                    </div>
                    <div class="success-item highlight">
                        <span class="label">Token(卡密)：</span>
                        <span id="memberToken" class="value"></span>
                        <button class="copy-btn" onclick="copyText('memberToken')">📋</button>
                    </div>
                </div>

                <div class="important-notice">
                    <h4>⚠️ 重要提醒</h4>
                    <p>Token(卡密)遗失后无法找回，请妥善保存！建议截图保存。</p>
                </div>

                <div class="support-notice">
                    <p>如有任何问题，请联系客服</p>
                    <div class="support-buttons">
                        <a href="https://t.me/Dataso" target="_blank" class="support-btn telegram">
                            📱 联系客服
                        </a>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="closeSuccessModal()">确定</button>
            </div>
        </div>
    </div>

    <!-- 加载中模态框 -->
    <div id="loadingModal" class="modal">
        <div class="modal-content loading-content">
            <div class="loading-spinner"></div>
            <p id="loadingText">正在处理中...</p>
        </div>
    </div>

    <script src="Public/static/js/vip.js"></script>
</body>
</html>
