# 🚀 会员购买系统部署检查清单

## 📋 部署前检查

### ✅ 文件完整性检查
- [ ] `vip.html` - 主页面文件
- [ ] `Public/static/css/vip.css` - 样式文件
- [ ] `Public/static/js/vip.js` - JavaScript文件
- [ ] `api/config.php` - 配置文件
- [ ] `api/create_order.php` - 创建订单API
- [ ] `api/check_payment.php` - 检查支付API
- [ ] `api/register_member.php` - 注册会员API
- [ ] `api/init_database.php` - 数据库初始化
- [ ] `database/vip_orders.sql` - 数据库表结构
- [ ] `test_vip.html` - 测试页面

### ✅ 依赖文件检查
- [ ] `Public/js/jquery-1.9.1.min.js` - jQuery库
- [ ] `Public/js/qrcode.js` - 二维码生成库
- [ ] `Public/layer/layer.js` - 弹层组件

### ✅ 服务器环境检查
- [ ] PHP版本 >= 7.0
- [ ] MySQL版本 >= 5.7
- [ ] 启用curl扩展
- [ ] 启用PDO扩展
- [ ] Web服务器配置正确

## 🔧 配置步骤

### 1. 数据库配置
编辑 `api/config.php`：
```php
define('DB_HOST', 'your_host');
define('DB_NAME', 'your_database');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 2. 上游API配置
确认上游API地址正确：
- 创建订单：`https://cloudshop.qnm6.top/create_order.php`
- 检查支付：`https://cloudshop.qnm6.top/check_payment_status.php`
- 注册会员：`https://api.qnm6.top/admapi/vipregister.php`

### 3. 目录权限设置
```bash
chmod 755 logs/
chmod 644 api/*.php
chmod 644 *.html
```

## 🧪 功能测试

### 基础功能测试
- [ ] 访问 `vip.html` 页面正常显示
- [ ] 响应式设计在不同设备上正常
- [ ] 套餐选择功能正常
- [ ] 支付方式选择功能正常

### API接口测试
- [ ] 访问 `api/init_database.php` 初始化数据库
- [ ] 测试创建订单API
- [ ] 测试检查支付API
- [ ] 测试注册会员API

### 完整流程测试
- [ ] 选择月度会员套餐
- [ ] 选择支付方式
- [ ] 创建订单成功
- [ ] 二维码正常生成
- [ ] 支付状态检测正常
- [ ] 会员注册成功
- [ ] 防重复注册机制正常

## 🎨 界面检查

### 视觉效果
- [ ] 像素风格样式正确
- [ ] 配色方案符合要求（蓝色、橙色、紫色）
- [ ] 动画效果流畅
- [ ] 模态框显示正常

### 用户体验
- [ ] 客服联系入口显示正确
- [ ] 投诉入口链接正确
- [ ] 复制功能正常工作
- [ ] 错误提示友好
- [ ] 加载状态显示正常

## 🔍 安全检查

### 数据安全
- [ ] SQL注入防护
- [ ] 参数验证完整
- [ ] 错误信息不泄露敏感数据
- [ ] 日志记录适当

### 业务安全
- [ ] 防重复注册机制
- [ ] 订单状态验证
- [ ] 支付状态验证
- [ ] 会员类型验证

## 📱 兼容性测试

### 浏览器兼容性
- [ ] Chrome (最新版本)
- [ ] Firefox (最新版本)
- [ ] Safari (最新版本)
- [ ] Edge (最新版本)
- [ ] 移动端浏览器

### 设备兼容性
- [ ] 桌面端 (>1200px)
- [ ] 平板端 (768px-1200px)
- [ ] 手机端 (<768px)

## 📊 性能检查

### 页面性能
- [ ] 页面加载速度 < 3秒
- [ ] 图片优化
- [ ] CSS/JS文件压缩
- [ ] 缓存策略配置

### API性能
- [ ] API响应时间 < 2秒
- [ ] 数据库查询优化
- [ ] 错误处理完善
- [ ] 日志记录合理

## 🚨 上线前最终检查

### 配置确认
- [ ] 生产环境数据库配置
- [ ] 上游API地址确认
- [ ] 客服联系方式确认
- [ ] 投诉地址确认

### 备份准备
- [ ] 数据库备份
- [ ] 代码备份
- [ ] 配置文件备份

### 监控准备
- [ ] 日志监控配置
- [ ] 错误报警配置
- [ ] 性能监控配置

## 📞 应急联系

### 技术支持
- 客服TG: https://t.me/Dataso
- 投诉地址: https://cloudshop.qnm6.top/tousu.html

### 常见问题处理
1. **支付失败** - 引导用户联系客服
2. **注册失败** - 检查日志，联系技术支持
3. **页面异常** - 清除缓存，刷新页面
4. **API错误** - 查看日志文件，检查网络连接

---

**部署完成后，请使用 `test_vip.html` 进行全面测试！**
